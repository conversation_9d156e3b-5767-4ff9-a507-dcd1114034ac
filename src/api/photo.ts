import { http } from '@/http/request/alova'

/**
 * 证件照数据项
 */
export interface IPhotoItem {
  id: number
  name: string
  widthPx: number
  heightPx: number
  widthMm: number
  heightMm: number
  icon: number
  sort: number
  category: number
  dpi: number
}

/**
 * 分页请求参数
 */
export interface IPhotoListParams {
  pageNum: number
  pageSize: number
  type: number // 分类类型：1-常用尺寸，2-各类证件，3-各类签证
}

/**
 * 搜索请求参数
 */
export interface IPhotoSearchParams {
  pageNum: number
  pageSize: number
  type: number // 固定为0，表示搜索
  name: string // 搜索关键词
}

/**
 * 分页响应数据
 */
export interface IPhotoListResponse {
  records: IPhotoItem[]
  total: number
  size: number
  current: number
  pages: number
}

/**
 * 获取美颜效果状态
 * @returns Promise<number> 返回美颜效果状态值
 */
export function getWebGlow() {
  return http.Post<number>(
    '/api/getWebGlow',
    {}, // POST 请求的 data，空对象
    { // 配置对象
      meta: {
        ignoreAuth: true, // 忽略认证，不需要登录即可访问
      },
    },
  )
}

/**
 * 获取证件照列表
 * @param params 分页参数
 * @returns Promise<IPhotoListResponse>
 */
export function getPhotoList(params: IPhotoListParams) {
  return http.Get<IPhotoListResponse>('/item/itemList', {
    params,
    meta: {
      ignoreAuth: true, // 忽略认证，不需要登录即可访问
    },
  })
}

/**
 * 搜索证件照
 * @param params 搜索参数
 * @returns Promise<IPhotoListResponse>
 */
export function searchPhotoList(params: IPhotoSearchParams) {
  return http.Get<IPhotoListResponse>('/item/itemList', {
    params,
    meta: {
      ignoreAuth: true, // 忽略认证，不需要登录即可访问
    },
  })
}

/**
 * 创建高清图请求参数
 */
export interface ICreateHdPhotoParams {
  image: string // 原始图片标识
  type: number // 证件照类型：category == 4 ? 0 : 1
  itemId: number // 规格ID
  isBeautyOn: number // 是否美颜：开启为1，没开启为0
}

/**
 * 创建证件照返回数据
 */
export interface ICreatePhotoResponse {
  cimg: string | null // 当前显示的图片
  dpi: number | null // 分辨率
  id2: number | null // 图片ID
  kimg: string | null // 高清图片数据 (base64格式)
  msg: string | null // 消息
  oimg: string | null // 原始图片数据 (base64格式)
  picUrl: string | null // 图片URL
}

/**
 * 创建标清图
 * @param params 创建标清图参数
 * @returns Promise<ICreatePhotoResponse> 返回创建结果
 */
export function createIdPhoto(params: ICreateHdPhotoParams) {
  return http.Post<ICreatePhotoResponse>('/api/createIdPhoto', params, {
    meta: {
      toast: false, // 关闭错误提示，由调用方处理
    },
  })
}

/**
 * 创建高清图
 * @param params 创建高清图参数
 * @returns Promise<any> 返回创建结果
 */
export function createIdHdPhoto(params: ICreateHdPhotoParams) {
  return http.Post<ICreatePhotoResponse>('/api/createIdHdPhoto', params, {
    meta: {
      toast: false, // 关闭错误提示，由调用方处理
    },
  })
}

/**
 * 换背景请求参数
 */
export interface IUpdateColorParams {
  image: string // 图片标识
  colors: string // 背景颜色
  kb: number // kb参数
  render: number // render参数
  dpi: number // 分辨率：type=1时为实际dpi值，高清下载时为0
}

/**
 * 换背景返回数据
 */
export interface IUpdateColorResponse {
  cimg: string | null // 标清图片数据
}

/**
 * 换背景
 * @param params 换背景参数
 * @returns Promise<IUpdateColorResponse> 返回换背景结果
 */
export function updateIdPhoto(params: IUpdateColorParams) {
  return http.Post<IUpdateColorResponse>('/api/updateIdPhoto', params, {
    meta: {
      toast: false, // 关闭错误提示，由调用方处理
    },
  })
}

/**
 * 保存照片请求参数
 */
export interface ISavePhotoParams {
  image: string // 图片数据
  photoId: number // 照片ID
}

/**
 * 保存照片返回数据
 */
export interface ISavePhotoResponse {
  picUrl: string // 图片URL
}

/**
 * 保存照片
 * @param params 保存照片参数
 * @returns Promise<ISavePhotoResponse> 返回保存结果
 */
export function savePhoto(params: ISavePhotoParams) {
  return http.Post<ISavePhotoResponse>('/api/updateUserPhonto', params, {
    meta: {
      toast: false, // 关闭错误提示，由调用方处理
    },
  })
}

/**
 * 相册照片项
 */
export interface IAlbumPhotoItem {
  id: number
  userId: number | null
  name: string
  size: string
  createTime: string
  oimg: string | null
  nimg: string
}

/**
 * 相册列表请求参数
 */
export interface IAlbumListParams {
  pageNum: number
  pageSize: number
}

/**
 * 相册列表响应数据
 */
export interface IAlbumListResponse {
  records: IAlbumPhotoItem[]
  total: number
  size: number
  current: number
  pages: number
}

/**
 * 获取相册列表
 * @param params 分页参数
 * @returns Promise<IAlbumListResponse>
 */
export function getAlbumList(params: IAlbumListParams) {
  return http.Get<IAlbumListResponse>('/item/photoList', {
    params,
    cacheFor: 0, // 禁用缓存
    meta: {
      toast: false, // 关闭错误提示，由调用方处理
    },
  })
}

/**
 * 删除相册照片请求参数
 */
export interface IDeletePhotoParams {
  id: number
}

/**
 * 删除相册照片
 * @param params 删除参数
 * @returns Promise<any> 返回删除结果
 */
export function deletePhoto(params: IDeletePhotoParams) {
  return http.Get('/item/deletePhotoId', {
    params,
    meta: {
      toast: false, // 关闭错误提示，由调用方处理
    },
  })
}

/**
 * 探索统计数据
 */
export interface IExploreCountResponse {
  zjzCount: number // 证件照数量
  generateLayoutCount: number // 生成布局数量
  colourizeCount: number // 上色数量
  mattingCount: number // 抠图数量
  cartoonCount: number // 卡通数量
  editImageCount: number // 编辑图片数量
}

/**
 * 获取探索统计数据
 * @returns Promise<IExploreCountResponse> 返回统计数据
 */
export function getExploreCount() {
  return http.Get<IExploreCountResponse>('/otherApi/exploreCount', {
    meta: {
      toast: false, // 关闭错误提示，由调用方处理
    },
  })
}

/**
 * 检查免费配额请求参数
 */
export interface ICheckFreeQuotaParams {
  type: number
  type2: number
}

/**
 * 检查免费配额
 * @param params 请求参数
 * @returns Promise<number> 返回配额状态
 */
export function checkTheFreeQuota(params: ICheckFreeQuotaParams) {
  return http.Get<number>('/otherApi/checkTheFreeQuota', {
    params,
    meta: {
      toast: false, // 关闭错误提示，由调用方处理
    },
  })
}

/**
 * 视频单元数据
 */
export interface IVideoUnitResponse {
  videoUnitId: string
  downloadHd: number
}

/**
 * 获取视频单元
 * @returns Promise<IVideoUnitResponse> 返回视频单元数据
 */
export function getVideoUnit() {
  return http.Post<IVideoUnitResponse>('/api/getvideoUnit', {}, {
    meta: {
      toast: false, // 关闭错误提示，由调用方处理
    },
  })
}

/**
 * 抠图请求参数
 */
export interface IMattingParams {
  processedImage: string // 处理后的图片
}

/**
 * 抠图处理
 * @param params 抠图参数
 * @returns Promise<string> 返回抠图结果
 */
export function matting(params: IMattingParams) {
  return http.Post<string>('/otherApi/matting', params, {
    meta: {
      toast: false, // 关闭错误提示，由调用方处理
    },
  })
}
