<script setup lang="ts">
import { computed } from 'vue'

interface ColorOption {
  name: string
  color: string
}

interface Props {
  modelValue: boolean
  currentColor: string
  colorOptions?: ColorOption[]
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'colorChange', color: string): void
}

const props = withDefaults(defineProps<Props>(), {
  colorOptions: () => [
    { name: 'white', color: '#ffffff' },
    { name: 'blue', color: '#428eda' },
    { name: 'red', color: '#FF0000' },
    { name: 'light-blue', color: '#539fed' },
    { name: 'gray', color: '#b3b3b3' },
    { name: 'dark-red', color: '#ac020b' },
    { name: 'navy', color: '#2c4c80' },
    { name: 'pink', color: '#ffdad8' },
    { name: 'yellow', color: '#fef7c6' },
    { name: 'mint', color: '#d1f0dc' },
    { name: 'purple', color: '#e4d7f3' },
    { name: 'cyan', color: '#c0e8e7' },
    { name: 'periwinkle', color: '#d0ddf5' },
  ],
})

const emit = defineEmits<Emits>()

// 计算属性：控制弹窗显示状态
const showPicker = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit('update:modelValue', value),
})

// 选择颜色
function selectColor(color: string) {
  emit('colorChange', color)
  emit('update:modelValue', false) // 选择颜色后关闭弹窗
}
</script>

<template>
  <!-- 颜色选择弹窗 -->
  <wd-popup
    v-model="showPicker"
    position="bottom"
    closable
    custom-style="border-radius: 32rpx 32rpx 0 0; padding: 30rpx;"
  >
    <view class="w-full">
      <!-- 弹窗标题 -->
      <view class="mb-[40rpx] text-center text-[30rpx] text-[#333]">
        选择背景颜色
      </view>

      <!-- 颜色选择区域 -->
      <view class="mb-[40rpx] flex flex-wrap items-center justify-center gap-[32rpx]">
        <view
          v-for="option in colorOptions"
          :key="option.name"
          class="relative h-[64rpx] min-w-[64rpx] flex-shrink-0 cursor-pointer rounded-[64rpx] text-0"
          :style="{
            backgroundColor: option.color,
            border: option.name === 'white' ? '2px solid #ddd' : 'none',
          }"
          @click="selectColor(option.color)"
        >
          <!-- 选中状态指示器 -->
          <view
            v-if="currentColor === option.color"
            class="absolute inset-0 flex items-center justify-center"
          >
            <view class="relative h-[24rpx] w-[24rpx] rounded-full bg-white shadow-sm">
              <view class="absolute left-1/2 top-1/2 h-[12rpx] w-[12rpx] rounded-full bg-red-500 -translate-x-1/2 -translate-y-1/2" />
            </view>
          </view>
        </view>
      </view>
    </view>
  </wd-popup>
</template>
