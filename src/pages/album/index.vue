<route lang="jsonc" type="page">
{
  "layout": "tabbar",
  "style": {
    "navigationStyle": "default",
    "navigationBarBackgroundColor": "#fff",
    "navigationBarTitleText": "历史相册"
  }
}
</route>

<script lang="ts" setup>
import type { IAlbumPhotoItem } from '@/api/photo'
import { deletePhoto, getAlbumList } from '@/api/photo'
import { useUserStore } from '@/store/user'

// 响应式数据
const albumList = ref<IAlbumPhotoItem[]>([])
const pagingRef = ref()

// 用户状态管理
const userStore = useUserStore()

// 检查是否已登录
const isLogined = computed(() => {
  return !!userStore.userInfo.token
})

// 跳转到登录页
function goToLogin() {
  uni.navigateTo({
    url: '/pages/login/login',
  })
}

// 查询相册列表数据
async function queryAlbumList(pageNo: number, pageSize: number) {
  try {
    // 调用真实API接口
    const response = await getAlbumList({
      pageNum: pageNo,
      pageSize,
    })

    // 使用 completeByTotal 方法，z-paging会自动判断是否还有更多数据
    pagingRef.value?.completeByTotal(response.records, response.total)
  }
  catch (error) {
    console.error('获取相册列表失败:', error)
    uni.showToast({
      title: '获取数据失败，请重试',
      icon: 'none',
    })
    // 请求失败时调用complete(false)
    pagingRef.value?.complete(false)
  }
}

// 点击相册项 - 查看详情
function handleAlbumClick(item: IAlbumPhotoItem) {
  // 跳转到相册详情页，传递完整的item数据
  uni.navigateTo({
    url: `/pagesPhoto/album-detail/index?data=${encodeURIComponent(JSON.stringify(item))}`,
  })
}

// 处理删除按钮点击
function handleDeleteClick(item: IAlbumPhotoItem) {
  handleDeleteAlbum(item)
}

// 删除相册项
async function handleDeleteAlbum(item: IAlbumPhotoItem) {
  uni.showModal({
    title: '确认删除',
    content: `确定要删除"${item.name}"吗？删除后无法恢复。`,
    success: async (res) => {
      if (res.confirm) {
        try {
          // 调用删除接口
          await deletePhoto({ id: item.id })
          uni.showToast({
            title: '删除成功',
            icon: 'success',
          })
          // 刷新列表
          pagingRef.value?.reload()
        }
        catch (error) {
          console.error('删除失败:', error)
          uni.showToast({
            title: '删除失败，请重试',
            icon: 'none',
          })
        }
      }
    },
  })
}
</script>

<template>
  <view class="album-container">
    <!-- 未登录状态 -->
    <view v-if="!isLogined" class="not-login-container">
      <view class="not-login-content">
        <view class="not-login-text">
          未登录
        </view>
        <view class="not-login-tip">
          请先登录后查看历史相册
        </view>
        <wd-button
          type="primary"
          custom-style="margin-top: 40rpx; border-radius: 50rpx; padding: 24rpx 60rpx;"
          @click="goToLogin"
        >
          去登录
        </wd-button>
      </view>
    </view>

    <!-- 已登录状态 - z-paging列表容器 -->
    <z-paging
      v-else
      ref="pagingRef"
      v-model="albumList"
      :default-page-size="10"
      @query="queryAlbumList"
    >
      <!-- 自定义空状态页面 -->
      <template #empty>
        <view class="custom-empty">
          <view class="empty-icon">
            📷
          </view>
          <view class="empty-text">
            暂无相册数据
          </view>
          <view class="empty-tip">
            下拉刷新试试
          </view>
        </view>
      </template>

      <!-- 相册列表项 -->
      <view v-for="item in albumList" :key="item.id" class="album-item">
        <wd-swipe-action>
          <!-- 主要内容 -->
          <view class="album-card" @click="handleAlbumClick(item)">
            <!-- 相册封面 -->
            <view class="album-cover">
              <image
                :src="item.nimg"
                mode="aspectFill"
                class="cover-image"
                :lazy-load="true"
              />
            </view>

            <!-- 相册信息 -->
            <view class="album-info">
              <view class="album-title">
                {{ item.name }}
              </view>
              <view class="album-description">
                尺寸：{{ item.size }}
              </view>
              <view class="album-time">
                {{ item.createTime }}
              </view>
            </view>

            <!-- 向右箭头 -->
            <view class="album-arrow">
              <wd-icon name="arrow-right" size="22px" />
            </view>
          </view>

          <!-- 右滑删除按钮 -->
          <template #right>
            <view class="swipe-action">
              <view class="delete-button" @click="handleDeleteClick(item)">
                删除
              </view>
            </view>
          </template>
        </wd-swipe-action>
      </view>
    </z-paging>
  </view>
</template>

<style lang="scss" scoped>
.album-container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.not-login-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
}

.not-login-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;

  .not-login-text {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 20rpx;
  }

  .not-login-tip {
    font-size: 28rpx;
    color: #666;
    line-height: 1.5;
  }
}

.custom-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;

  .empty-icon {
    font-size: 80rpx;
    margin-bottom: 20rpx;
  }

  .empty-text {
    font-size: 32rpx;
    color: #666;
    margin-bottom: 10rpx;
  }

  .empty-tip {
    font-size: 28rpx;
    color: #999;
  }
}

.album-item {
  margin: 0rpx 20rpx;
}

.album-card {
  background: #fff;
  border-radius: 10rpx;
  padding: 24rpx;
  margin-top: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
}

.album-cover {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  overflow: hidden;
  margin-right: 24rpx;

  .cover-image {
    width: 100%;
    height: 100%;
  }
}

.album-info {
  flex: 1;

  .album-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 8rpx;
  }

  .album-description {
    font-size: 28rpx;
    color: #666;
    margin-bottom: 8rpx;
    // 限制显示两行，超出显示省略号
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .album-time {
    font-size: 24rpx;
    color: #999;
    margin-bottom: 4rpx;
  }

  .album-id {
    font-size: 20rpx;
    color: #ccc;
  }
}

.album-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 16rpx;
  color: #ccc;
}

// 左滑操作样式
.swipe-action {
  height: 100%;
  display: flex;
  align-items: center;
}

.delete-button {
  background-color: #dd524d;
  color: #fff;
  height: 100%;
  padding: 0 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  border-radius: 0;
}
</style>
