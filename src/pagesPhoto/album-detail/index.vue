<route lang="jsonc" type="page">
{
  "layout": "default",
  "style": {
    // 导航条为白色
    "navigationBarBackgroundColor": "#fff",
    "navigationBarTitleText": "相册详情"
  }
}
</route>

<script setup lang="ts">
import type { IAlbumPhotoItem } from '@/api/photo'
import { onLoad } from '@dcloudio/uni-app'
import { ref } from 'vue'
import { useAlbumPermission } from '@/hooks/useAlbumPermission'
import { downloadImageToLocal, saveImageToAlbum } from '@/utils/imageUtils'

defineOptions({
  name: 'AlbumDetail',
})

// 相册详情数据
const albumDetail = ref<IAlbumPhotoItem>({
  id: 0,
  userId: null,
  name: '',
  size: '',
  createTime: '',
  oimg: null,
  nimg: '',
})

// 页面状态
const pageState = ref({
  loading: false,
  downloading: false,
})

// 使用相册权限管理钩子
const { checkPermission } = useAlbumPermission()

// 页面加载时获取传递的数据
onLoad((options) => {
  if (options.data) {
    try {
      const decodedData = decodeURIComponent(options.data)
      const itemData = JSON.parse(decodedData) as IAlbumPhotoItem
      albumDetail.value = itemData
      console.log('接收到的相册详情数据:', itemData)
    }
    catch (error) {
      console.error('解析相册详情数据失败:', error)
      uni.showToast({
        title: '数据解析失败',
        icon: 'none',
      })
    }
  }
  else {
    uni.showToast({
      title: '缺少必要参数',
      icon: 'none',
    })
  }
})

// 下载图片到相册
async function handleDownload() {
  try {
    // 检查相册权限
    const hasPermission = await checkPermission()
    if (!hasPermission) {
      return
    }

    pageState.value.downloading = true

    // 优先使用原图，如果没有则使用缩略图
    const imageUrl = albumDetail.value.oimg || albumDetail.value.nimg
    if (!imageUrl) {
      throw new Error('图片地址不存在')
    }

    console.log('开始下载图片:', imageUrl)

    // 下载网络图片到本地
    const localFilePath = await downloadImageToLocal(imageUrl)

    // 保存图片到相册
    await saveImageToAlbum(localFilePath)

    // 显示下载成功提示
    uni.showToast({
      title: '照片已保存到相册',
      icon: 'success',
    })
  }
  catch (error) {
    console.error('下载失败:', error)
  }
  finally {
    pageState.value.downloading = false
  }
}

// 预览图片
function handlePreviewImage() {
  const imageUrl = albumDetail.value.oimg || albumDetail.value.nimg
  if (!imageUrl) {
    uni.showToast({
      title: '图片地址不存在',
      icon: 'none',
    })
    return
  }

  uni.previewImage({
    urls: [imageUrl],
    current: imageUrl,
  })
}
</script>

<template>
  <view class="album-detail-container">
    <view class="wrapper">
      <view class="top">
        <view class="image-wrapper">
          <image
            class="photo"
            mode="aspectFit"
            :src="albumDetail.nimg"
            @click="handlePreviewImage"
          />
        </view>
      </view>
      <view class="bottom">
        <view class="specs">
          <view class="title">
            {{ albumDetail.name }}
          </view>
          <view class="infos">
            <text>{{ albumDetail.size }}</text>
            <view />
            {{ albumDetail.createTime }}
          </view>
        </view>
        <wd-button
          type="primary"
          :loading="pageState.downloading"
          :disabled="pageState.downloading"
          custom-style="height: 88rpx; width: 150rpx; border-radius: 40rpx; font-size: 30rpx; font-weight: 700; background: #8280ff; color: #fff; border: none;"
          @click="handleDownload"
        >
          {{ pageState.downloading ? '下载中...' : '下载' }}
        </wd-button>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.album-detail-container {
  background-color: #f5f7fa;
  height: 100vh;
  width: 100vw;
  padding: 30rpx;
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  align-items: center;
}

.wrapper {
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 30rpx 0 rgba(0, 0, 0, 0.1);
  max-width: 750rpx;
  width: 100%;
}

.top {
  display: flex;
  justify-content: center;
  padding: 50rpx 40rpx;
}

.image-wrapper {
  font-size: 0;
  position: relative;
}

.photo {
  width: 80vw;
  height: 60vh;
}

.bottom {
  align-items: center;
  background: #f2f6fc;
  display: flex;
  height: 160rpx;
  justify-content: space-between;
  padding: 42rpx 40rpx;
}

.specs {
  flex: 1;
}

.title {
  color: #3c464f;
  font-size: 28rpx;
  font-weight: 700;
  line-height: 36rpx;
}

.infos {
  color: #91979c;
  font-size: 24rpx;
  line-height: 36rpx;
  margin-top: 4rpx;

  view {
    border-right: 1px solid #d8d9df;
    display: inline-block;
    height: 24rpx;
    margin: 6rpx 16rpx;
    vertical-align: top;
    width: 0;
  }
}
</style>
