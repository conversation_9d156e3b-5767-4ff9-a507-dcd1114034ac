<route lang="jsonc" type="page">
{
  "layout": "default",
  "style": {
    // 导航条为白色
    "navigationBarBackgroundColor": "#fff",
    "navigationBarTitleText": "智能抠图"
  }
}
</route>

<script setup lang="ts">
import { onLoad } from '@dcloudio/uni-app'
import { ref } from 'vue'
import { checkTheFreeQuota, getExploreCount, getVideoUnit, matting } from '@/api/photo'
import { useAlbumPermission } from '@/hooks/useAlbumPermission'
import { getEnvBaseUploadUrl } from '@/utils'
import { downloadImageToLocal, saveImageToAlbum } from '@/utils/imageUtils'
import { useUpload } from '@/utils/uploadFile'

defineOptions({
  name: 'Cutout',
})

// 使用相册权限管理钩子
const { checkPermission } = useAlbumPermission()

// 页面数据状态
const cutoutPageData = ref({
  // 当前显示的图片URL
  currentImageUrl: '',
  // 原始图片url
  originalImageUrl: '',
  // 抠图处理状态
  processing: false,
  // 是否已完成抠图
  cutoutCompleted: false,
  // 图片实际显示尺寸
  imageDisplaySize: {
    width: 0,
    height: 0,
    top: 0,
    left: 0,
  },
  // 功能是否维护  == -1 代表功能维护中
  mattingCount: 0,
  // 免费配额 == -1 代表无限次, 0代表没有次数, >0 代表剩余次数
  quotaData: 0,
  // 是否能下载高清
  downloadHd: 1,
  // 视频单元ID
  videoUnitId: '',
  // 页面加载状态
  pageLoading: false,
  // 页面错误状态
  pageError: false,
})

// 页面加载时获取传递的数据
onLoad((options) => {
  cutoutPageData.value.currentImageUrl = decodeURIComponent(options.imageUrl)
  cutoutPageData.value.originalImageUrl = cutoutPageData.value.currentImageUrl

  // 调用探索统计接口
  fetchExploreCount()
})

// 获取探索统计数据
async function fetchExploreCount() {
  try {
    cutoutPageData.value.pageLoading = true
    cutoutPageData.value.pageError = false

    // 三个接口并行调用
    const [exploreData, quotaData, videoUnitData] = await Promise.all([
      getExploreCount(),
      checkTheFreeQuota({ type: 6, type2: 6 }),
      getVideoUnit(),
    ])

    cutoutPageData.value.mattingCount = exploreData.mattingCount
    cutoutPageData.value.quotaData = quotaData
    cutoutPageData.value.videoUnitId = videoUnitData.videoUnitId
    cutoutPageData.value.downloadHd = videoUnitData.downloadHd
    cutoutPageData.value.pageLoading = false
  }
  catch (error) {
    console.error('接口调用失败:', error)
    cutoutPageData.value.pageLoading = false
    cutoutPageData.value.pageError = true
  }
}

// 重试接口调用
function handleRetry() {
  fetchExploreCount()
}

// 处理图片加载完成事件
function handleImageLoad(e: any) {
  const { width: naturalWidth, height: naturalHeight } = e.detail

  // 获取容器尺寸（80vw 和 60vh）
  const containerWidth = uni.getSystemInfoSync().windowWidth * 0.8
  const containerHeight = uni.getSystemInfoSync().windowHeight * 0.6

  // 计算图片在 aspectFit 模式下的实际显示尺寸
  const containerRatio = containerWidth / containerHeight
  const imageRatio = naturalWidth / naturalHeight

  let displayWidth: number, displayHeight: number, top: number, left: number

  if (imageRatio > containerRatio) {
    // 图片更宽，以宽度为准
    displayWidth = containerWidth
    displayHeight = containerWidth / imageRatio
    top = (containerHeight - displayHeight) / 2
    left = 0
  }
  else {
    // 图片更高，以高度为准
    displayHeight = containerHeight
    displayWidth = containerHeight * imageRatio
    top = 0
    left = (containerWidth - displayWidth) / 2
  }

  // 更新图片实际显示尺寸
  cutoutPageData.value.imageDisplaySize = {
    width: displayWidth,
    height: displayHeight,
    top,
    left,
  }
}

// 开始抠图
async function handleStartCutout() {
  try {
    cutoutPageData.value.processing = true

    // 检查功能维护状态
    if (cutoutPageData.value.mattingCount === -1) {
      uni.showToast({
        title: '功能维护中，暂停使用',
        icon: 'none',
      })
      return
    }
    // 开始上传图片
    console.log('开始抠图处理，原始图片URL:', cutoutPageData.value.originalImageUrl)
    const uploadedImageUrl = await startUpload(cutoutPageData.value.originalImageUrl)

    if (cutoutPageData.value.quotaData === 0) {
      // 为什么在这里检查？是因为只有所有图片判断通过后才能进行检查，不然一个错误看一个广告，用户就要骂人了
      console.log('没有次数了，需要看广告, 但是现在没上广告,所以暂不处理')
    }
    // 开始抠图
    const mattingResult = await matting({ processedImage: uploadedImageUrl })
    cutoutPageData.value.currentImageUrl = mattingResult
    // 标记抠图完成
    cutoutPageData.value.cutoutCompleted = true
  }
  catch (error) {
    console.error('抠图处理失败:', error)
    uni.showToast({
      title: '处理失败，请重试',
      icon: 'none',
    })
  }
  finally {
    cutoutPageData.value.processing = false
  }
}

// 开始上传图片
async function startUpload(imagePath: string): Promise<string> {
  console.log('开始上传图片:', imagePath)

  return new Promise((resolve, reject) => {
    // 使用 useUpload 的 directFilePath 功能直接上传指定路径的文件
    const { run: upload } = useUpload(
      getEnvBaseUploadUrl(),
      {
        // token 由拦截器自动添加到 header 中，不需要在 formData 中传递
      },
      {
        maxSize: 10, // 最大10MB
        onProgress: (progress) => {
          console.log(`上传进度：${progress}%`)
        },
        onSuccess: (res) => {
          resolve(String(res))
        },
        onError: (err) => {
          console.error('上传失败', err)
          const errorMessage = err || '未知错误'
          // 抛出异常，让外层catch捕获
          reject(new Error(`上传失败：${errorMessage}`))
        },
      },
      imagePath, // 直接传入图片路径
    )

    // 执行上传
    upload()
  })
}

// 下载抠图
async function handleDownloadCutout() {
  try {
    // 检查相册权限
    const hasPermission = await checkPermission()
    if (!hasPermission) {
      return
    }

    if (!cutoutPageData.value.currentImageUrl) {
      uni.showToast({
        title: '没有可下载的图片',
        icon: 'none',
      })
      return
    }

    // 显示下载中的loading
    uni.showLoading({
      title: '保存中...',
      mask: true,
    })
    // 下载网络图片到本地
    const localFilePath = await downloadImageToLocal(cutoutPageData.value.currentImageUrl)

    // 保存图片到相册
    await saveImageToAlbum(localFilePath)

    // 隐藏loading
    uni.hideLoading()

    // 显示保存成功提示
    uni.showToast({
      title: '抠图已保存到相册',
      icon: 'none',
    })
  }
  catch (error) {
    // 隐藏loading
    uni.hideLoading()

    // 显示保存失败提示
    const errorMessage = error?.message || '保存失败'
    uni.showToast({
      title: errorMessage,
      icon: 'none',
    })
  }
}
</script>

<template>
  <view class="cutout-container">
    <!-- 页面加载中状态 -->
    <view v-if="cutoutPageData.pageLoading" class="loading-page">
      <wd-loading color="#8280ff" />
      <text class="loading-page-text">
        加载中...
      </text>
    </view>

    <!-- 网络错误状态 -->
    <view v-else-if="cutoutPageData.pageError" class="error-page">
      <view class="error-content">
        <text class="error-title">
          网络请求失败
        </text>
        <text class="error-desc">
          请检查网络连接后重试
        </text>
        <wd-button
          type="primary"
          custom-style="height: 88rpx; width: 300rpx; border-radius: 40rpx; font-size: 30rpx; font-weight: 700; background: #8280ff; color: #fff; border: none; margin-top: 40rpx;"
          @click="handleRetry"
        >
          点击重试
        </wd-button>
      </view>
    </view>

    <!-- 正常页面内容 -->
    <view v-else class="wrapper">
      <view class="top">
        <view class="image-wrapper">
          <view class="image-container">
            <image
              class="photo"
              mode="aspectFit"
              :src="cutoutPageData.currentImageUrl"
              @load="handleImageLoad"
            />

            <!-- 抠图处理状态遮罩 -->
            <view
              v-if="cutoutPageData.processing"
              class="loading-overlay"
              :style="{
                width: `${cutoutPageData.imageDisplaySize.width}px`,
                height: `${cutoutPageData.imageDisplaySize.height}px`,
                top: `${cutoutPageData.imageDisplaySize.top}px`,
                left: `${cutoutPageData.imageDisplaySize.left}px`,
              }"
            >
              <view class="loading-content">
                <wd-loading color="#ffffff" />
                <text class="loading-text">
                  抠图处理中...
                </text>
              </view>
            </view>
          </view>
        </view>
      </view>
      <view class="bottom">
        <!-- 开始抠图按钮 -->
        <wd-button
          v-if="!cutoutPageData.cutoutCompleted"
          type="primary"
          :loading="cutoutPageData.processing"
          :disabled="cutoutPageData.processing"
          custom-style="height: 88rpx; width: 80vw; border-radius: 40rpx; font-size: 30rpx; font-weight: 700; background: #8280ff; color: #fff; border: none;"
          @click="handleStartCutout"
        >
          {{ cutoutPageData.processing ? '处理中...' : '开始抠图' }}
        </wd-button>

        <!-- 下载抠图按钮 -->
        <wd-button
          v-if="cutoutPageData.cutoutCompleted"
          type="primary"
          custom-style="height: 88rpx; width: 80vw; border-radius: 40rpx; font-size: 30rpx; font-weight: 700; background: #8280ff; color: #fff; border: none;"
          @click="handleDownloadCutout"
        >
          下载抠图
        </wd-button>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.cutout-container {
  background-color: #f5f7fa;
  height: 100vh;
  width: 100vw;
  padding: 30rpx;
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-page {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.loading-page-text {
  margin-top: 30rpx;
  font-size: 32rpx;
  color: #666;
}

.error-page {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.error-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.error-desc {
  font-size: 28rpx;
  color: #666;
}

.wrapper {
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 30rpx 0 rgba(0, 0, 0, 0.1);
  max-width: 750rpx;
  width: 100%;
}

.top {
  display: flex;
  justify-content: center;
  padding: 50rpx 40rpx;
}

.image-wrapper {
  font-size: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.image-container {
  position: relative;
  display: inline-block;
}

.photo {
  width: 80vw;
  height: 60vh;
  display: block;
}

.loading-overlay {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8rpx;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: white;
}

.loading-text {
  margin-top: 16rpx;
  font-size: 28rpx;
  color: #ffffff;
}

.bottom {
  align-items: center;
  background: #f2f6fc;
  display: flex;
  height: 160rpx;
  justify-content: center;
  padding: 42rpx 40rpx;
}
</style>
