<route lang="jsonc" type="page">
{
  "layout": "default",
  "style": {
    "navigationBarTitleText": "预览",
    "backgroundColor": "#F5F7FA"
  }
}
</route>

<script setup lang="ts">
import type { ICreatePhotoResponse, IPhotoItem } from '@/api/photo'
import { onLoad } from '@dcloudio/uni-app'
import { getCurrentInstance, ref } from 'vue'
import { createIdPhoto, savePhoto, updateIdPhoto } from '@/api/photo'
import ColorPicker from '@/components/ColorPicker.vue'
import { useAlbumPermission } from '@/hooks/useAlbumPermission'
import { getEnvBaseUploadUrl } from '@/utils'
import { downloadImageToLocal, saveImageToAlbum } from '@/utils/imageUtils'
import { useUpload } from '@/utils/uploadFile'

defineOptions({
  name: 'PhotoPreview',
})
type LocalImageData = Omit<ICreatePhotoResponse, 'cimg'>
// 图片数据 - 与 createIdPhoto 返回的数据格式一致
const imageData = ref<LocalImageData>({
  dpi: null,
  id2: null,
  kimg: null,
  msg: null,
  oimg: null,
  picUrl: null,
})

// 当前页面接口用到的参数
const pageState = ref({
  cimg: '', // 当前页面显示的图片
  kb: 0, // 图片文件大小
  render: 0, // 渲染参数
  beautyEnabled: false, // 美颜开关状态
  currentBgColor: '#ffffff', // 当前背景颜色
  showColorPicker: false, // 颜色选择弹窗显示状态
  uploadLoading: false, // 上传状态
})

// 规格详情数据
const specDetail = ref<IPhotoItem | null>(null)

// 处理通过 EventChannel 接收的数据
function handleEventChannelData(data: any) {
  // 初始化时只设置 kimg，其他字段保持初始值
  pageState.value.cimg = data.imageUrl
  specDetail.value = data.specData
  pageState.value.beautyEnabled = data.beautyEnabled
  // 某学校要求
  if (specDetail.value.category === 1 && specDetail.value.id === 759) {
    pageState.value.kb = 30
  }
  startUpload(data.imageUrl)
}

// 调用更换背景色接口
async function callUpdateIdPhoto(type: number) {
  const updateColorParams = {
    image: imageData.value.kimg,
    colors: pageState.value.currentBgColor,
    kb: pageState.value.kb,
    render: pageState.value.render,
    dpi: 0,
  }
  if (type === 1) {
    updateColorParams.dpi = imageData.value.dpi
  }
  const updateColorResult = await updateIdPhoto(updateColorParams)
  pageState.value.cimg = updateColorResult.cimg
}

// 创建标清图
async function createPhoto(uploadedImageUrl: string) {
  try {
    const params = {
      image: uploadedImageUrl,
      type: specDetail.value.category === 4 ? 0 : 1,
      itemId: specDetail.value.id,
      isBeautyOn: pageState.value.beautyEnabled ? 1 : 0,
    }
    const result = await createIdPhoto(params)
    imageData.value = result
    // createIdPhoto 成功后，调用更换背景色的 API 接口
    await callUpdateIdPhoto(1)

    // 制作成功，关闭loading状态
    pageState.value.uploadLoading = false

    uni.showToast({
      title: '制作成功',
      icon: 'success',
    })
  }
  catch (error) {
    // 制作失败，关闭loading状态
    pageState.value.uploadLoading = false

    // 显示失败弹窗
    const errorMessage = error?.message || '未知错误'
    uni.showModal({
      title: '制作失败',
      content: `制作失败：${errorMessage}`,
      showCancel: false,
      confirmText: '确定',
      success: () => {
        // 点击确定后返回上一页
        uni.navigateBack()
      },
    })
  }
}

// 开始上传图片
function startUpload(imagePath: string) {
  pageState.value.uploadLoading = true

  // 使用 useUpload 的 directFilePath 功能直接上传指定路径的文件
  const { run: upload } = useUpload(
    getEnvBaseUploadUrl(),
    {
      // token 由拦截器自动添加到 header 中，不需要在 formData 中传递
    },
    {
      maxSize: 15, // 最大15MB
      onProgress: (progress) => {
        console.log(`上传进度：${progress}%`)
      },
      onSuccess: (res) => {
        // 上传成功后调用创建接口
        createPhoto(String(res))
      },
      onError: (err) => {
        console.error('上传失败', err)
        pageState.value.uploadLoading = false
        const errorMessage = err || '未知错误'
        uni.showModal({
          title: '制作失败',
          content: `制作失败：${errorMessage}, 请检查图片是否符合要求`,
          showCancel: false,
          confirmText: '确定',
          success: () => {
            // 点击确定后返回上一页
            uni.navigateBack()
          },
        })
      },

    },
    imagePath, // 直接传入图片路径
  )

  // 执行上传
  upload()
}

// 页面加载时获取传递的图片数据和规格详情数据
onLoad(() => {
  // 优先使用 EventChannel 接收数据
  const instance = getCurrentInstance().proxy
  const eventChannel = (instance as any)?.getOpenerEventChannel?.()
  eventChannel.on('acceptDataFromSpecPage', handleEventChannelData)
})

// 打开颜色选择器
function openColorPicker() {
  pageState.value.showColorPicker = true
}

// 处理颜色改变
async function handleColorChange(color: string) {
  const oldColor = pageState.value.currentBgColor
  pageState.value.currentBgColor = color
  pageState.value.uploadLoading = true
  try {
    await callUpdateIdPhoto(1)
  }
  catch (error) {
    pageState.value.currentBgColor = oldColor
    console.error('调用换背景接口失败:', error)
    uni.showToast({
      title: '更换背景色失败',
      icon: 'none',
    })
  }
  finally {
    pageState.value.uploadLoading = false
  }
}

// 使用相册权限管理钩子
const { checkPermission } = useAlbumPermission()

// 执行保存照片操作
async function handleSavePhoto() {
  try {
    // 检查相册权限
    const hasPermission = await checkPermission()
    if (!hasPermission) {
      return
    }

    // 显示保存中的loading
    uni.showLoading({
      title: '保存中...',
      mask: true,
    })

    // 调用保存照片API
    const saveParams = {
      image: pageState.value.cimg,
      photoId: imageData.value.id2 || 0, // 照片ID，如果没有则传0
    }
    const result = await savePhoto(saveParams)

    console.log('保存照片成功:', result.picUrl)

    // 下载网络图片到本地
    const localFilePath = await downloadImageToLocal(result.picUrl)

    // 保存图片到相册
    await saveImageToAlbum(localFilePath)

    // 隐藏loading
    uni.hideLoading()

    // 显示保存成功提示
    uni.showToast({
      title: '照片已保存到相册',
      icon: 'none',
    })
  }
  catch (error) {
    // 隐藏loading
    uni.hideLoading()

    // 显示保存失败提示
    const errorMessage = error?.message || '保存失败'
    uni.showToast({
      title: errorMessage,
      icon: 'none',
    })

    console.error('保存照片失败:', error)
  }
}

// 打开保存照片确认弹窗
function openSavePhoto() {
  uni.showModal({
    title: '保存照片',
    content: `确定要保存照片吗？`,
    success: (res) => {
      if (res.confirm) {
        handleSavePhoto()
      }
    },
  })
}
</script>

<template>
  <!-- 主容器 -->
  <view class="h-screen w-full flex flex-col">
    <!-- 照片预览区域 - flex: 1 占据剩余空间并居中 -->
    <view class="flex flex-1 items-center justify-center">
      <view class="relative inline-block border-4 border-[#edf2fa] text-0">
        <text class="absolute left-[-20rpx] top-[-20rpx] h-[24rpx] w-[24rpx]" style="border-left: 6rpx solid #e7eef8; border-top: 6rpx solid #e7eef8;" />
        <text class="absolute right-[-20rpx] top-[-20rpx] h-[24rpx] w-[24rpx]" style="border-right: 6rpx solid #e7eef8; border-top: 6rpx solid #e7eef8;" />
        <text class="absolute bottom-[-20rpx] left-[-20rpx] h-[24rpx] w-[24rpx]" style="border-left: 6rpx solid #e7eef8; border-bottom: 6rpx solid #e7eef8;" />
        <text class="absolute bottom-[-20rpx] right-[-20rpx] h-[24rpx] w-[24rpx]" style="border-right: 6rpx solid #e7eef8; border-bottom: 6rpx solid #e7eef8;" />
        <image class="h-auto w-[490rpx]" mode="widthFix" :src="pageState.cimg" />

        <!-- 制作状态遮罩 -->
        <view v-if="pageState.uploadLoading" class="absolute inset-0 flex items-center justify-center rounded bg-black bg-opacity-50">
          <view class="flex flex-col items-center text-white">
            <wd-loading color="#ffffff" />
            <text class="mt-2 text-sm">
              制作中...
            </text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部按钮区域 - 固定高度 -->
    <view class="h-[148rpx] flex items-center justify-center p-[0_40rpx]">
      <wd-button
        plain
        custom-style="height: 88rpx; margin: 0 20rpx; width: 325rpx; border-radius: 100px; font-size: 34rpx; font-weight: 700; background: rgba(159, 159, 255, 0.3); color: #8280FF; border: none;"
        @click="openColorPicker"
      >
        换底色
      </wd-button>
      <wd-button
        custom-style="height: 88rpx; margin: 0 20rpx; width: 325rpx; border-radius: 100px; font-size: 34rpx; font-weight: 700; background: #8280FF; color: #FFFFFF; border: none;"
        @click="openSavePhoto()"
      >
        保存照片
      </wd-button>
    </view>
  </view>

  <!-- 颜色选择弹窗 -->
  <ColorPicker
    v-model="pageState.showColorPicker"
    :current-color="pageState.currentBgColor"
    @color-change="handleColorChange"
  />
</template>
