/**
 * 图片处理工具函数集合
 * 包含下载、保存、压缩等纯函数工具
 */

/**
 * 下载结果接口
 */
export interface DownloadResult {
  /** 本地临时文件路径 */
  tempFilePath: string
  /** HTTP状态码 */
  statusCode: number
}

/**
 * 保存结果接口
 */
export interface SaveResult {
  /** 保存成功的消息 */
  errMsg: string
  /** 保存路径（仅App端支持） */
  path?: string
}

/**
 * 下载网络图片到本地
 * @param url 图片网络地址
 * @returns Promise<string> 本地临时文件路径
 * @throws Error 下载失败时抛出错误
 *
 * @example
 * try {
 *   const localPath = await downloadImageToLocal('https://example.com/image.jpg')
 *   console.log('下载成功:', localPath)
 * } catch (error) {
 *   console.error('下载失败:', error.message)
 * }
 */
export function downloadImageToLocal(url: string): Promise<string> {
  return new Promise<string>((resolve, reject) => {
    if (!url) {
      reject(new Error('图片URL不能为空'))
      return
    }

    uni.downloadFile({
      url,
      success: (res) => {
        if (res.statusCode === 200) {
          resolve(res.tempFilePath)
        }
        else {
          reject(new Error(`下载失败，HTTP状态码: ${res.statusCode}`))
        }
      },
      fail: (err) => {
        const errorMessage = err.errMsg || err.message || '网络下载失败'
        reject(new Error(`下载失败: ${errorMessage}`))
      },
    })
  })
}

/**
 * 保存图片到系统相册
 * @param filePath 本地图片文件路径
 * @returns Promise<SaveResult> 保存结果
 * @throws Error 保存失败时抛出错误
 *
 * @example
 * try {
 *   const result = await saveImageToAlbum('/tmp/image.jpg')
 *   console.log('保存成功:', result.errMsg)
 * } catch (error) {
 *   console.error('保存失败:', error.message)
 * }
 */
export function saveImageToAlbum(filePath: string): Promise<SaveResult> {
  return new Promise<SaveResult>((resolve, reject) => {
    if (!filePath) {
      reject(new Error('文件路径不能为空'))
      return
    }

    uni.saveImageToPhotosAlbum({
      filePath,
      success: (res) => {
        resolve({
          errMsg: res.errMsg,
          path: (res as any).path, // App端可能返回保存路径
        })
      },
      fail: (err) => {
        const errorMessage = err.errMsg || err.message || '保存到相册失败'
        reject(new Error(`保存失败: ${errorMessage}`))
      },
    })
  })
}

/**
 * 批量下载图片
 * @param urls 图片URL数组
 * @param options 下载选项
 * @returns Promise<string[]> 本地文件路径数组
 *
 * @example
 * const urls = ['url1', 'url2', 'url3']
 * const localPaths = await downloadImagesInBatch(urls, { maxConcurrent: 3 })
 */
export async function downloadImagesInBatch(
  urls: string[],
  options: {
    /** 最大并发数，默认3 */
    maxConcurrent?: number
    /** 失败时是否继续，默认true */
    continueOnError?: boolean
  } = {},
): Promise<string[]> {
  const { maxConcurrent = 3, continueOnError = true } = options
  const results: string[] = []

  // 分批处理
  for (let i = 0; i < urls.length; i += maxConcurrent) {
    const batch = urls.slice(i, i + maxConcurrent)
    const batchPromises = batch.map(async (url, index) => {
      try {
        return await downloadImageToLocal(url)
      }
      catch (error) {
        if (continueOnError) {
          console.warn(`下载第${i + index + 1}张图片失败:`, error)
          return null
        }
        else {
          throw error
        }
      }
    })

    const batchResults = await Promise.all(batchPromises)
    results.push(...batchResults.filter(Boolean) as string[])
  }

  return results
}

/**
 * 获取图片信息
 * @param src 图片路径
 * @returns Promise<UniApp.GetImageInfoSuccessData> 图片信息
 *
 * @example
 * const info = await getImageInfo('/tmp/image.jpg')
 * console.log('图片尺寸:', info.width, 'x', info.height)
 */
export function getImageInfo(src: string): Promise<UniApp.GetImageInfoSuccessData> {
  return new Promise((resolve, reject) => {
    uni.getImageInfo({
      src,
      success: resolve,
      fail: reject,
    })
  })
}

/**
 * 压缩图片
 * @param src 图片路径
 * @param options 压缩选项
 * @returns Promise<string> 压缩后的临时文件路径
 *
 * @example
 * const compressedPath = await compressImage('/tmp/image.jpg', { quality: 80 })
 */
export function compressImage(
  src: string,
  options: {
    /** 压缩质量，0-100 */
    quality?: number
    /** 压缩后的宽度 */
    width?: string
    /** 压缩后的高度 */
    height?: string
  } = {},
): Promise<string> {
  return new Promise((resolve, reject) => {
    uni.compressImage({
      src,
      quality: options.quality || 80,
      width: options.width,
      height: options.height,
      success: res => resolve(res.tempFilePath),
      fail: reject,
    })
  })
}

/**
 * 检查图片文件大小
 * @param filePath 文件路径
 * @param maxSizeMB 最大大小限制（MB）
 * @returns Promise<boolean> 是否符合大小要求
 */
export async function checkImageSize(filePath: string, maxSizeMB: number): Promise<boolean> {
  try {
    const fileInfo = await uni.getFileInfo({ filePath })
    const sizeMB = fileInfo.size / 1024 / 1024
    return sizeMB <= maxSizeMB
  }
  catch (error) {
    console.warn('检查文件大小失败:', error)
    return false
  }
}

/**
 * 图片格式转换（通过canvas）
 * @param imagePath 图片路径
 * @param format 目标格式
 * @param quality 质量（0-1）
 * @returns Promise<string> 转换后的临时文件路径
 */
export function convertImageFormat(
  imagePath: string,
  format: 'jpg' | 'png' = 'jpg',
  quality: number = 0.8,
): Promise<string> {
  return new Promise((resolve, reject) => {
    // 这里需要结合具体的canvas实现
    // 暂时返回原路径
    resolve(imagePath)
  })
}
