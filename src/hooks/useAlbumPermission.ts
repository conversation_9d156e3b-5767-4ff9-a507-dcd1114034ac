import { ref } from 'vue'

/**
 * 相册权限管理钩子函数
 * @example
 * const { checkPermission, hasPermission, isChecking } = useAlbumPermission()
 *
 * // 检查并请求权限
 * const permitted = await checkPermission()
 * if (permitted) {
 *   // 执行需要权限的操作
 * }
 */

/**
 * 权限状态枚举
 */
export enum PermissionStatus {
  /** 未检查 */
  NOT_DETERMINED = 'not_determined',
  /** 已授权 */
  AUTHORIZED = 'authorized',
  /** 已拒绝 */
  DENIED = 'denied',
  /** 配置错误 */
  CONFIG_ERROR = 'config_error',
}

/**
 * 相册权限管理钩子
 */
export function useAlbumPermission() {
  /** 当前权限状态 */
  const permissionStatus = ref<PermissionStatus>(PermissionStatus.NOT_DETERMINED)
  /** 是否正在检查权限 */
  const isChecking = ref(false)
  /** 是否有权限（计算属性） */
  const hasPermission = ref(false)

  /**
   * 打开系统权限设置页面
   */
  const openPermissionSettings = () => {
    // #ifdef APP-PLUS
    uni.openAppAuthorizeSetting({
      success: () => {
        console.log('打开权限设置成功')
      },
      fail: (err) => {
        console.error('打开权限设置失败:', err)
      },
    })
    // #endif
    // #ifdef MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ || MP-KUAISHOU || MP-JD
    uni.openSetting({
      success: (res) => {
        console.log('权限设置结果:', res.authSetting)
      },
      fail: (err) => {
        console.error('打开设置失败:', err)
      },
    })
    // #endif
  }

  /**
   * 显示权限提示弹窗
   * @param onConfirm 用户点击确认后的回调
   */
  const showPermissionModal = (onConfirm?: () => void) => {
    uni.showModal({
      title: '权限提示',
      content: '需要相册权限才能保存照片，请前往设置开启',
      confirmText: '去设置',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          openPermissionSettings()
          onConfirm?.()
        }
      },
    })
  }

  /**
   * 检查App端权限状态
   * @returns 权限状态
   */
  const checkAppPermission = (): PermissionStatus => {
    // #ifdef APP-PLUS
    try {
      const appAuthorizeSetting = uni.getAppAuthorizeSetting()
      const albumAuth = appAuthorizeSetting.albumAuthorized

      if (albumAuth === 'authorized') {
        return PermissionStatus.AUTHORIZED
      }
      else if (albumAuth === 'denied') {
        return PermissionStatus.DENIED
      }
      else if ((albumAuth as string) === 'config error') {
        return PermissionStatus.CONFIG_ERROR
      }
      else {
        return PermissionStatus.NOT_DETERMINED
      }
    }
    catch (error) {
      console.warn('检查App权限失败:', error)
    }
    // #endif

    return PermissionStatus.NOT_DETERMINED
  }

  /**
   * 请求相册权限
   * @returns Promise<boolean> 是否获得权限
   */
  const requestPermission = (): Promise<boolean> => {
    return new Promise((resolve) => {
      uni.authorize({
        scope: 'scope.writePhotosAlbum',
        success: () => {
          permissionStatus.value = PermissionStatus.AUTHORIZED
          hasPermission.value = true
          resolve(true)
        },
        fail: () => {
          permissionStatus.value = PermissionStatus.DENIED
          hasPermission.value = false
          showPermissionModal()
          resolve(false)
        },
      })
    })
  }

  /**
   * 检查并请求相册权限
   * @returns Promise<boolean> 是否有权限
   */
  const checkPermission = async (): Promise<boolean> => {
    if (isChecking.value) {
      return hasPermission.value
    }

    isChecking.value = true

    try {
      // 先检查App端权限状态
      const appPermissionStatus = checkAppPermission()

      if (appPermissionStatus === PermissionStatus.AUTHORIZED) {
        permissionStatus.value = PermissionStatus.AUTHORIZED
        hasPermission.value = true
        return true
      }

      if (appPermissionStatus === PermissionStatus.DENIED) {
        permissionStatus.value = PermissionStatus.DENIED
        hasPermission.value = false
        showPermissionModal()
        return false
      }

      if (appPermissionStatus === PermissionStatus.CONFIG_ERROR) {
        permissionStatus.value = PermissionStatus.CONFIG_ERROR
        hasPermission.value = false
        uni.showModal({
          title: '配置错误',
          content: '应用权限配置有误，请联系开发者',
          showCancel: false,
        })
        return false
      }

      // 如果App端权限未确定或非App端，则请求权限
      return await requestPermission()
    }
    finally {
      isChecking.value = false
    }
  }

  /**
   * 重置权限状态
   */
  const resetPermission = () => {
    permissionStatus.value = PermissionStatus.NOT_DETERMINED
    hasPermission.value = false
    isChecking.value = false
  }

  return {
    /** 当前权限状态 */
    permissionStatus,
    /** 是否正在检查权限 */
    isChecking,
    /** 是否有权限 */
    hasPermission,
    /** 检查并请求权限 */
    checkPermission,
    /** 打开权限设置页面 */
    openPermissionSettings,
    /** 显示权限提示弹窗 */
    showPermissionModal,
    /** 重置权限状态 */
    resetPermission,
  }
}
